# مثال لعرض خصائص المواد المختلفة في القائمة الفرعية
# هذا الملف يوضح كيف تظهر الخصائص المختلفة في القائمة

import bpy

def show_material_properties_example():
    """عرض مثال على خصائص المواد المختلفة"""
    
    print("=== مثال على عرض خصائص المواد ===\n")
    
    # التحقق من وجود كائن محدد
    obj = bpy.context.object
    if not obj or not obj.active_material:
        print("❌ لا يوجد كائن محدد أو مادة نشطة")
        return
    
    material = obj.active_material
    print(f"📦 Material: {material.name}")
    print(f"🔧 Uses Nodes: {material.use_nodes}")
    
    if not material.use_nodes:
        print("⚠️  Material doesn't use nodes - Enable nodes to see properties")
        return
    
    nodes = material.node_tree.nodes
    
    # البحث عن عقدة Principled BSDF
    principled_nodes = [n for n in nodes if n.type == 'BSDF_PRINCIPLED']
    if principled_nodes:
        principled = principled_nodes[0]
        print(f"\n🎨 Principled BSDF Properties:")
        print("=" * 50)
        
        # تجميع الخصائص حسب النوع
        color_properties = []
        numeric_properties = []
        vector_properties = []
        connected_properties = []
        
        for inp in principled.inputs:
            if hasattr(inp, 'default_value'):
                if inp.is_linked:
                    connected_properties.append(inp.name)
                else:
                    # تحديد نوع الخاصية
                    if hasattr(inp.default_value, '__len__') and len(inp.default_value) == 4:
                        # لون (RGBA)
                        color_properties.append((inp.name, inp.default_value))
                    elif hasattr(inp.default_value, '__len__') and len(inp.default_value) == 3:
                        # متجه (Vector)
                        vector_properties.append((inp.name, inp.default_value))
                    else:
                        # رقم
                        numeric_properties.append((inp.name, inp.default_value))
        
        # عرض الخصائص الرقمية
        if numeric_properties:
            print("\n📊 Numeric Properties:")
            for name, value in numeric_properties:
                print(f"  • {name}: {value}")
        
        # عرض خصائص الألوان
        if color_properties:
            print("\n🎨 Color Properties:")
            for name, value in color_properties:
                r, g, b, a = value
                print(f"  • {name}: R={r:.3f}, G={g:.3f}, B={b:.3f}, A={a:.3f}")
        
        # عرض خصائص المتجهات
        if vector_properties:
            print("\n📐 Vector Properties:")
            for name, value in vector_properties:
                x, y, z = value
                print(f"  • {name}: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
        
        # عرض الخصائص المتصلة
        if connected_properties:
            print("\n🔗 Connected Properties:")
            for name in connected_properties:
                print(f"  • {name}: Connected")
    
    # البحث عن العقد المجمعة
    group_nodes = [n for n in nodes if n.type == 'GROUP']
    if group_nodes:
        print(f"\n🔧 Group Nodes ({len(group_nodes)} found):")
        print("=" * 50)
        
        for group_node in group_nodes:
            print(f"\n📦 Group Node: {group_node.name}")
            
            editable_inputs = [inp for inp in group_node.inputs 
                             if not inp.is_linked and hasattr(inp, 'default_value')]
            connected_inputs = [inp for inp in group_node.inputs if inp.is_linked]
            
            if editable_inputs:
                print("  📝 Editable Inputs:")
                for inp in editable_inputs:
                    print(f"    • {inp.name}: {inp.default_value}")
            
            if connected_inputs:
                print("  🔗 Connected Inputs:")
                for inp in connected_inputs:
                    print(f"    • {inp.name}: Connected")
            
            if not editable_inputs and not connected_inputs:
                print("  ❌ No inputs found")
    
    print(f"\n✅ هذا ما ستراه في القائمة الفرعية 'خصائص المادة النشطة'")
    print("=" * 60)

# تشغيل المثال
if __name__ == "__main__":
    show_material_properties_example()
