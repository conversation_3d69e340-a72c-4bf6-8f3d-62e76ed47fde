# إضافة قائمة خصائص المادة النشطة لـ KH-Tools

## الوصف
هذه الإضافة تضيف قائمة فرعية جديدة إلى قائمة "Basic Material" في إضافة KH-Tools، تتيح لك التحكم المباشر في خصائص المادة النشطة للكائن المحدد.

## المميزات
- عرض وتحرير خصائص المادة النشطة مباشرة من قائمة Basic Material
- دعم عقدة Principled BSDF مع جميع خصائصها الأساسية
- دعم العقد المجمعة (Group Nodes) المخصصة
- واجهة باللغة العربية
- تحديث فوري للتغييرات

## الخصائص المدعومة

### خصائص Principled BSDF الأساسية:
- **اللون الأساسي** (Base Color)
- **المعدنية** (Metallic)
- **الخشونة** (Roughness)
- **الشفافية** (Alpha)
- **لون الانبعاث** (Emission Color)
- **قوة الانبعاث** (Emission Strength)
- **معامل الانكسار** (IOR)

### العقد المجمعة:
- عرض جميع المدخلات القابلة للتعديل
- ترجمة أسماء المدخلات الشائعة إلى العربية
- دعم أنواع مختلفة من المدخلات (ألوان، أرقام، متجهات)

## طريقة الاستخدام

### التثبيت:
1. انسخ ملف `kh_material_properties_addon.py` إلى مجلد إضافات Blender
2. فعل الإضافة من إعدادات Blender
3. أو قم بتشغيل الكود مباشرة في Blender

### الاستخدام:
1. حدد كائن من نوع Mesh في المشهد
2. تأكد من وجود مادة نشطة للكائن
3. اذهب إلى قائمة KH-Tools في الشريط الجانبي
4. افتح قائمة "Basic Material"
5. ستجد قائمة فرعية جديدة بعنوان "خصائص المادة النشطة"
6. اضغط عليها لعرض وتحرير خصائص المادة

## المتطلبات
- Blender 4.0 أو أحدث
- إضافة KH-Tools مثبتة ومفعلة
- كائن من نوع Mesh محدد
- مادة نشطة مرتبطة بالكائن

## الملاحظات
- القائمة تظهر فقط عند وجود كائن محدد مع مادة نشطة
- إذا كانت المادة لا تستخدم العقد، ستظهر خيار لتفعيل العقد
- المدخلات المتصلة بعقد أخرى تظهر كـ "متصل" ولا يمكن تحريرها مباشرة
- التغييرات تطبق فوراً على المادة

## استكشاف الأخطاء
- إذا لم تظهر القائمة، تأكد من تحديد كائن Mesh مع مادة نشطة
- إذا لم تظهر خصائص للتحكم، تأكد من أن المادة تستخدم العقد
- إذا كانت الخصائص تظهر كـ "متصل"، فهذا يعني أنها مرتبطة بعقد أخرى

## الدعم الفني
هذه الإضافة مصممة للعمل مع إضافة KH-Tools وتتطلب وجودها للعمل بشكل صحيح.

## الإصدار
الإصدار 1.0 - إصدار أولي مع الميزات الأساسية
