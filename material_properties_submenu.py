import bpy
from bpy.types import Panel, Operator
from bpy.utils import register_class, unregister_class

bl_info = {
    "name": "قائمة خصائص المادة النشطة",
    "author": "KH-Tools Extension",
    "version": (1, 0),
    "blender": (4, 0, 0),
    "location": "View3D > UI > KH-Tools > Basic Material",
    "description": "إضافة قائمة فرعية لعرض وتحرير خصائص المادة النشطة للكائن المحدد",
    "category": "Material",
}

# فئة لإنشاء قائمة فرعية لخصائص المادة النشطة
class MATERIAL_PT_active_properties_submenu(Panel):
    """قائمة فرعية لعرض خصائص المادة النشطة للكائن المحدد"""
    bl_label = "خصائص المادة النشطة"
    bl_idname = "MATERIAL_PT_active_properties"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "KH-Tools"
    bl_parent_id = "OBJECT_PT_Basic_materials"  # ربط بقائمة Basic Material
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """التحقق من وجود كائن محدد من نوع MESH مع مادة نشطة"""
        obj = context.object
        return (obj is not None and 
                obj.type == 'MESH' and 
                obj.active_material is not None and
                obj.active_material.use_nodes)

    def draw_header(self, context):
        """رسم رأس القائمة"""
        self.layout.label(text="", icon='MATERIAL')

    def draw(self, context):
        """رسم محتوى القائمة"""
        layout = self.layout
        obj = context.object
        material = obj.active_material
        
        if material and material.use_nodes:
            # البحث عن عقدة Principled BSDF أو العقد المجمعة
            principled_node = None
            group_node = None

            for node in material.node_tree.nodes:
                if node.type == 'BSDF_PRINCIPLED':
                    principled_node = node
                    break
                elif node.type == 'GROUP' and node.node_tree:
                    # البحث عن Principled BSDF داخل العقدة المجمعة
                    for inner_node in node.node_tree.nodes:
                        if inner_node.type == 'BSDF_PRINCIPLED':
                            group_node = node
                            principled_node = inner_node
                            break
                    if principled_node:
                        break

            if principled_node:
                # عرض معلومات المادة
                info_box = layout.box()
                info_box.label(text=f"المادة: {material.name}", icon='MATERIAL')
                if group_node:
                    info_box.label(text=f"العقدة المجمعة: {group_node.name}", icon='NODETREE')

                # إنشاء صندوق للخصائص الأساسية
                box = layout.box()
                box.label(text="الخصائص الأساسية", icon='MATERIAL_DATA')

                # التحقق من وجود المدخلات وعرضها
                inputs_to_show = [
                    ('Base Color', 'اللون الأساسي', 'COLORSET_01_VEC'),
                    ('Metallic', 'المعدنية', 'METALLIC'),
                    ('Roughness', 'الخشونة', 'ROUGHNESS'),
                    ('Alpha', 'الشفافية', 'ALPHA')
                ]

                for input_name, arabic_name, icon in inputs_to_show:
                    if input_name in principled_node.inputs:
                        input_socket = principled_node.inputs[input_name]
                        row = box.row()
                        if input_socket.is_linked:
                            row.label(text=f"{arabic_name}: متصل", icon='LINKED')
                        else:
                            row.prop(input_socket, "default_value", text=arabic_name)
                    else:
                        # البحث عن أسماء بديلة للمدخلات
                        alt_names = {
                            'Base Color': ['BaseColor', 'Diffuse', 'Color'],
                            'Metallic': ['Metal'],
                            'Roughness': ['Rough'],
                            'Alpha': ['Transparency', 'Opacity']
                        }

                        found = False
                        if input_name in alt_names:
                            for alt_name in alt_names[input_name]:
                                if alt_name in principled_node.inputs:
                                    input_socket = principled_node.inputs[alt_name]
                                    row = box.row()
                                    if input_socket.is_linked:
                                        row.label(text=f"{arabic_name}: متصل", icon='LINKED')
                                    else:
                                        row.prop(input_socket, "default_value", text=arabic_name)
                                    found = True
                                    break
                
                # صندوق للخصائص المتقدمة
                box2 = layout.box()
                box2.label(text="الخصائص المتقدمة", icon='SETTINGS')
                
                # الانبعاث (Emission)
                if 'Emission Color' in principled_node.inputs:
                    row = box2.row()
                    row.prop(principled_node.inputs['Emission Color'], "default_value", text="لون الانبعاث")
                
                if 'Emission Strength' in principled_node.inputs:
                    row = box2.row()
                    row.prop(principled_node.inputs['Emission Strength'], "default_value", text="قوة الانبعاث")
                
                # معامل الانكسار (IOR)
                if 'IOR' in principled_node.inputs:
                    row = box2.row()
                    row.prop(principled_node.inputs['IOR'], "default_value", text="معامل الانكسار")
                
                # الانتقال (Transmission)
                if 'Transmission Weight' in principled_node.inputs:
                    row = box2.row()
                    row.prop(principled_node.inputs['Transmission Weight'], "default_value", text="الانتقال")
                
                # التشتت الفرعي (Subsurface)
                if 'Subsurface Weight' in principled_node.inputs:
                    row = box2.row()
                    row.prop(principled_node.inputs['Subsurface Weight'], "default_value", text="التشتت الفرعي")
                
                # صندوق لخصائص السطح
                box3 = layout.box()
                box3.label(text="خصائص السطح", icon='SURFACE_DATA')
                
                # العادي (Normal)
                if 'Normal' in principled_node.inputs:
                    row = box3.row()
                    if principled_node.inputs['Normal'].is_linked:
                        row.label(text="العادي: متصل", icon='LINKED')
                    else:
                        row.label(text="العادي: غير متصل", icon='UNLINKED')
                
                # الإزاحة (Displacement) - إذا كانت متوفرة
                material_output = None
                for node in material.node_tree.nodes:
                    if node.type == 'OUTPUT_MATERIAL':
                        material_output = node
                        break
                
                if material_output and 'Displacement' in material_output.inputs:
                    row = box3.row()
                    if material_output.inputs['Displacement'].is_linked:
                        row.label(text="الإزاحة: متصلة", icon='LINKED')
                    else:
                        row.label(text="الإزاحة: غير متصلة", icon='UNLINKED')
                
            else:
                # إذا لم توجد عقدة Principled BSDF، تحقق من العقد المجمعة
                group_nodes = [node for node in material.node_tree.nodes if node.type == 'GROUP']

                if group_nodes:
                    # عرض العقد المجمعة المتاحة
                    box = layout.box()
                    box.label(text="العقد المجمعة المتاحة", icon='NODETREE')

                    for group_node in group_nodes:
                        group_box = box.box()
                        group_box.label(text=f"العقدة: {group_node.name}", icon='GROUP')

                        # عرض مدخلات العقدة المجمعة
                        if group_node.inputs:
                            for input_socket in group_node.inputs:
                                if not input_socket.is_linked and hasattr(input_socket, 'default_value'):
                                    row = group_box.row()
                                    # ترجمة أسماء المدخلات الشائعة
                                    input_translations = {
                                        'Color': 'اللون',
                                        'Base Color': 'اللون الأساسي',
                                        'Roughness': 'الخشونة',
                                        'Metallic': 'المعدنية',
                                        'Alpha': 'الشفافية',
                                        'Strength': 'القوة',
                                        'Scale': 'المقياس',
                                        'Detail': 'التفاصيل',
                                        'Distortion': 'التشويه'
                                    }

                                    display_name = input_translations.get(input_socket.name, input_socket.name)
                                    row.prop(input_socket, "default_value", text=display_name)
                else:
                    # إذا لم توجد أي عقد مفيدة
                    box = layout.box()
                    row = box.row()
                    row.label(text="لا توجد عقد قابلة للتحكم", icon='ERROR')
                    row = box.row()
                    row.operator("material.add_principled_bsdf", text="إضافة Principled BSDF")
        
        else:
            # إذا لم تكن المادة تستخدم العقد
            box = layout.box()
            row = box.row()
            row.label(text="المادة لا تستخدم العقد", icon='ERROR')
            row = box.row()
            row.operator("material.enable_nodes", text="تفعيل العقد")


# عملية لتفعيل العقد في المادة
class MATERIAL_OT_enable_nodes(Operator):
    """تفعيل العقد في المادة النشطة"""
    bl_idname = "material.enable_nodes"
    bl_label = "تفعيل العقد"
    bl_description = "تفعيل نظام العقد في المادة النشطة"
    
    def execute(self, context):
        obj = context.object
        if obj and obj.active_material:
            obj.active_material.use_nodes = True
            self.report({'INFO'}, "تم تفعيل العقد في المادة")
        else:
            self.report({'ERROR'}, "لا توجد مادة نشطة")
        return {'FINISHED'}


# عملية لإضافة عقدة Principled BSDF
class MATERIAL_OT_add_principled_bsdf(Operator):
    """إضافة عقدة Principled BSDF إلى المادة"""
    bl_idname = "material.add_principled_bsdf"
    bl_label = "إضافة Principled BSDF"
    bl_description = "إضافة عقدة Principled BSDF إلى المادة النشطة"
    
    def execute(self, context):
        obj = context.object
        if obj and obj.active_material and obj.active_material.use_nodes:
            material = obj.active_material
            nodes = material.node_tree.nodes
            
            # إضافة عقدة Principled BSDF
            principled = nodes.new(type='ShaderNodeBsdfPrincipled')
            principled.location = (0, 0)
            
            # البحث عن عقدة الإخراج وربطها
            output_node = None
            for node in nodes:
                if node.type == 'OUTPUT_MATERIAL':
                    output_node = node
                    break
            
            if output_node:
                material.node_tree.links.new(principled.outputs['BSDF'], output_node.inputs['Surface'])
            
            self.report({'INFO'}, "تم إضافة عقدة Principled BSDF")
        else:
            self.report({'ERROR'}, "المادة غير متوفرة أو العقد غير مفعلة")
        return {'FINISHED'}


# قائمة الفئات للتسجيل
classes = [
    MATERIAL_PT_active_properties_submenu,
    MATERIAL_OT_enable_nodes,
    MATERIAL_OT_add_principled_bsdf,
]


def register():
    """تسجيل الفئات"""
    for cls in classes:
        register_class(cls)


def unregister():
    """إلغاء تسجيل الفئات"""
    for cls in reversed(classes):
        unregister_class(cls)


if __name__ == "__main__":
    register()
