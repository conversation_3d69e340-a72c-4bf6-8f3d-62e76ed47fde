import bpy
from bpy.types import Panel, Operator
from bpy.utils import register_class, unregister_class

bl_info = {
    "name": "KH - قائمة خصائص المادة النشطة",
    "author": "KH-Tools Extension",
    "version": (1, 0),
    "blender": (4, 0, 0),
    "location": "View3D > UI > KH-Tools > Basic Material",
    "description": "إضافة قائمة فرعية لعرض وتحرير خصائص المادة النشطة للكائن المحدد",
    "category": "Material",
}


class MATERIAL_PT_active_properties_submenu(Panel):
    """قائمة فرعية لعرض خصائص المادة النشطة للكائن المحدد"""
    bl_label = "خصائص المادة النشطة"
    bl_idname = "MATERIAL_PT_active_properties_submenu"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "KH-Tools"
    bl_parent_id = "OBJECT_PT_Basic_materials"  # ربط بقائمة Basic Material
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """التحقق من وجود كائن محدد من نوع MESH مع مادة نشطة"""
        obj = context.object
        return (obj is not None and 
                obj.type == 'MESH' and 
                obj.active_material is not None)

    def draw_header(self, context):
        """رسم رأس القائمة"""
        self.layout.label(text="", icon='MATERIAL')

    def draw(self, context):
        """رسم محتوى القائمة"""
        layout = self.layout
        obj = context.object
        material = obj.active_material
        
        # عرض معلومات المادة
        info_box = layout.box()
        info_box.label(text=f"المادة: {material.name}", icon='MATERIAL')
        
        if material.use_nodes:
            nodes = material.node_tree.nodes
            
            # البحث عن عقدة Principled BSDF
            principled_node = None
            for node in nodes:
                if node.type == 'BSDF_PRINCIPLED':
                    principled_node = node
                    break
            
            if principled_node:
                # عرض الخصائص الأساسية لـ Principled BSDF
                self.draw_principled_properties(layout, principled_node)
            
            # البحث عن العقد المجمعة
            group_nodes = [node for node in nodes if node.type == 'GROUP']
            if group_nodes:
                self.draw_group_nodes_properties(layout, group_nodes)
            
            # إذا لم توجد عقد مفيدة
            if not principled_node and not group_nodes:
                box = layout.box()
                box.label(text="لا توجد عقد قابلة للتحكم", icon='INFO')
                
        else:
            # المادة لا تستخدم العقد
            box = layout.box()
            box.label(text="المادة لا تستخدم العقد", icon='INFO')
            row = box.row()
            row.operator("material.enable_nodes", text="تفعيل العقد", icon='NODETREE')

    def draw_principled_properties(self, layout, principled_node):
        """رسم خصائص عقدة Principled BSDF"""
        box = layout.box()
        box.label(text="Principled BSDF", icon='MATERIAL_DATA')
        
        # الخصائص الأساسية مع الترجمة العربية
        basic_properties = [
            ('Base Color', 'اللون الأساسي', 'COLORSET_01_VEC'),
            ('Metallic', 'المعدنية', 'METALLIC'),
            ('Roughness', 'الخشونة', 'ROUGHNESS'),
            ('Alpha', 'الشفافية', 'MOD_OPACITY'),
            ('Emission Color', 'لون الانبعاث', 'LIGHT'),
            ('Emission Strength', 'قوة الانبعاث', 'LIGHT_SUN'),
            ('IOR', 'معامل الانكسار', 'MATERIAL'),
        ]
        
        for prop_name, arabic_name, icon in basic_properties:
            if prop_name in principled_node.inputs:
                input_socket = principled_node.inputs[prop_name]
                row = box.row()
                
                if input_socket.is_linked:
                    # إذا كان المدخل متصل بعقدة أخرى
                    row.label(text=f"{arabic_name}: متصل", icon='LINKED')
                else:
                    # إذا كان المدخل غير متصل، اعرض التحكم
                    row.prop(input_socket, "default_value", text=arabic_name)

    def draw_group_nodes_properties(self, layout, group_nodes):
        """رسم خصائص العقد المجمعة"""
        for group_node in group_nodes:
            box = layout.box()
            box.label(text=f"العقدة المجمعة: {group_node.name}", icon='NODETREE')
            
            # عرض مدخلات العقدة المجمعة القابلة للتعديل
            editable_inputs = [inp for inp in group_node.inputs 
                             if not inp.is_linked and hasattr(inp, 'default_value')]
            
            if editable_inputs:
                for input_socket in editable_inputs:
                    row = box.row()
                    # ترجمة أسماء المدخلات الشائعة
                    arabic_name = self.translate_input_name(input_socket.name)
                    row.prop(input_socket, "default_value", text=arabic_name)
            else:
                row = box.row()
                row.label(text="جميع المدخلات متصلة", icon='LINKED')

    def translate_input_name(self, input_name):
        """ترجمة أسماء المدخلات إلى العربية"""
        translations = {
            'Color': 'اللون',
            'Base Color': 'اللون الأساسي',
            'Roughness': 'الخشونة',
            'Metallic': 'المعدنية',
            'Alpha': 'الشفافية',
            'Strength': 'القوة',
            'Scale': 'المقياس',
            'Detail': 'التفاصيل',
            'Distortion': 'التشويه',
            'Saturation': 'التشبع',
            'Brightness': 'السطوع',
            'Contrast': 'التباين',
            'Hue': 'درجة اللون',
            'Value': 'القيمة',
            'Factor': 'المعامل',
            'Mix': 'المزج',
            'Fac': 'المعامل',
            'Normal': 'العادي',
            'Bump': 'النتوء',
            'Displacement': 'الإزاحة'
        }
        return translations.get(input_name, input_name)


class MATERIAL_OT_enable_nodes(Operator):
    """تفعيل العقد في المادة النشطة"""
    bl_idname = "material.enable_nodes"
    bl_label = "تفعيل العقد"
    bl_description = "تفعيل نظام العقد في المادة النشطة"
    
    def execute(self, context):
        obj = context.object
        if obj and obj.active_material:
            obj.active_material.use_nodes = True
            self.report({'INFO'}, "تم تفعيل العقد في المادة")
        else:
            self.report({'ERROR'}, "لا توجد مادة نشطة")
        return {'FINISHED'}


# قائمة الفئات للتسجيل
classes = [
    MATERIAL_PT_active_properties_submenu,
    MATERIAL_OT_enable_nodes,
]


def register():
    """تسجيل الفئات"""
    for cls in classes:
        register_class(cls)


def unregister():
    """إلغاء تسجيل الفئات"""
    for cls in reversed(classes):
        unregister_class(cls)


if __name__ == "__main__":
    register()
