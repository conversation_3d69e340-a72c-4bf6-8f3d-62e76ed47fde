bl_info = {
    "name": "Basic Material Properties",
    "author": "Your Name",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "3D Viewport > Sidebar > Basic Material",
    "description": "إضافة لعرض وتحرير خصائص المواد النشطة للكائنات المحددة",
    "category": "Material",
}

import bpy
from . import ui_panels
from . import operators

def register():
    """تسجيل جميع فئات الإضافة"""
    ui_panels.register()
    operators.register()

def unregister():
    """إلغاء تسجيل جميع فئات الإضافة"""
    ui_panels.unregister()
    operators.unregister()

if __name__ == "__main__":
    register()
