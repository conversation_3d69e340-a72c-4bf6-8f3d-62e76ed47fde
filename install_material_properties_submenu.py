# ملف التثبيت السريع لإضافة قائمة خصائص المادة النشطة
# قم بتشغيل هذا الكود في Blender لتثبيت الإضافة مباشرة

import bpy
from bpy.types import Panel, Operator
from bpy.utils import register_class, unregister_class

print("بدء تثبيت إضافة قائمة خصائص المادة النشطة...")

# إلغاء تسجيل أي إضافات مشابهة موجودة مسبقاً
try:
    # البحث عن القوائم المسجلة مسبقاً وإلغاء تسجيلها
    existing_panels = []
    for cls in bpy.types.Panel.__subclasses__():
        if hasattr(cls, 'bl_idname') and 'active_properties' in cls.bl_idname:
            existing_panels.append(cls)
    
    for panel in existing_panels:
        try:
            unregister_class(panel)
            print(f"تم إلغاء تسجيل القائمة: {panel.bl_idname}")
        except:
            pass
            
except Exception as e:
    print(f"تحذير: {e}")

# تعريف الإضافة الجديدة
class MATERIAL_PT_active_properties_submenu(Panel):
    """قائمة فرعية لعرض خصائص المادة النشطة للكائن المحدد"""
    bl_label = "خصائص المادة النشطة"
    bl_idname = "MATERIAL_PT_active_properties_submenu"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "KH-Tools"
    bl_parent_id = "OBJECT_PT_Basic_materials"  # ربط بقائمة Basic Material
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """التحقق من وجود كائن محدد من نوع MESH مع مادة نشطة"""
        obj = context.object
        return (obj is not None and 
                obj.type == 'MESH' and 
                obj.active_material is not None)

    def draw_header(self, context):
        """رسم رأس القائمة"""
        self.layout.label(text="", icon='MATERIAL')

    def draw(self, context):
        """رسم محتوى القائمة"""
        layout = self.layout
        obj = context.object
        material = obj.active_material
        
        # عرض معلومات المادة
        info_box = layout.box()
        info_box.label(text=f"المادة: {material.name}", icon='MATERIAL')
        
        if material.use_nodes:
            nodes = material.node_tree.nodes
            
            # البحث عن عقدة Principled BSDF
            principled_node = None
            for node in nodes:
                if node.type == 'BSDF_PRINCIPLED':
                    principled_node = node
                    break
            
            if principled_node:
                # عرض الخصائص الأساسية لـ Principled BSDF
                box = layout.box()
                box.label(text="Principled BSDF", icon='MATERIAL_DATA')
                
                # الخصائص الأساسية مع الترجمة العربية
                basic_properties = [
                    ('Base Color', 'اللون الأساسي'),
                    ('Metallic', 'المعدنية'),
                    ('Roughness', 'الخشونة'),
                    ('Alpha', 'الشفافية'),
                    ('Emission Color', 'لون الانبعاث'),
                    ('Emission Strength', 'قوة الانبعاث'),
                    ('IOR', 'معامل الانكسار'),
                ]
                
                for prop_name, arabic_name in basic_properties:
                    if prop_name in principled_node.inputs:
                        input_socket = principled_node.inputs[prop_name]
                        row = box.row()
                        
                        if input_socket.is_linked:
                            # إذا كان المدخل متصل بعقدة أخرى
                            row.label(text=f"{arabic_name}: متصل", icon='LINKED')
                        else:
                            # إذا كان المدخل غير متصل، اعرض التحكم
                            row.prop(input_socket, "default_value", text=arabic_name)
            
            # البحث عن العقد المجمعة
            group_nodes = [node for node in nodes if node.type == 'GROUP']
            if group_nodes:
                for group_node in group_nodes:
                    box = layout.box()
                    box.label(text=f"العقدة المجمعة: {group_node.name}", icon='NODETREE')
                    
                    # عرض مدخلات العقدة المجمعة القابلة للتعديل
                    editable_inputs = [inp for inp in group_node.inputs 
                                     if not inp.is_linked and hasattr(inp, 'default_value')]
                    
                    if editable_inputs:
                        for input_socket in editable_inputs:
                            row = box.row()
                            # ترجمة أسماء المدخلات الشائعة
                            translations = {
                                'Color': 'اللون', 'Base Color': 'اللون الأساسي',
                                'Roughness': 'الخشونة', 'Metallic': 'المعدنية',
                                'Alpha': 'الشفافية', 'Strength': 'القوة',
                                'Scale': 'المقياس', 'Detail': 'التفاصيل',
                                'Saturation': 'التشبع', 'Brightness': 'السطوع'
                            }
                            arabic_name = translations.get(input_socket.name, input_socket.name)
                            row.prop(input_socket, "default_value", text=arabic_name)
                    else:
                        row = box.row()
                        row.label(text="جميع المدخلات متصلة", icon='LINKED')
            
            # إذا لم توجد عقد مفيدة
            if not principled_node and not group_nodes:
                box = layout.box()
                box.label(text="لا توجد عقد قابلة للتحكم", icon='INFO')
                
        else:
            # المادة لا تستخدم العقد
            box = layout.box()
            box.label(text="المادة لا تستخدم العقد", icon='INFO')
            row = box.row()
            row.operator("material.enable_nodes_quick", text="تفعيل العقد", icon='NODETREE')


class MATERIAL_OT_enable_nodes_quick(Operator):
    """تفعيل العقد في المادة النشطة"""
    bl_idname = "material.enable_nodes_quick"
    bl_label = "تفعيل العقد"
    bl_description = "تفعيل نظام العقد في المادة النشطة"
    
    def execute(self, context):
        obj = context.object
        if obj and obj.active_material:
            obj.active_material.use_nodes = True
            self.report({'INFO'}, "تم تفعيل العقد في المادة")
        else:
            self.report({'ERROR'}, "لا توجد مادة نشطة")
        return {'FINISHED'}


# تسجيل الفئات
classes = [
    MATERIAL_PT_active_properties_submenu,
    MATERIAL_OT_enable_nodes_quick,
]

print("تسجيل الفئات...")
for cls in classes:
    try:
        register_class(cls)
        print(f"تم تسجيل: {cls.__name__}")
    except Exception as e:
        print(f"خطأ في تسجيل {cls.__name__}: {e}")

print("\n✅ تم تثبيت إضافة قائمة خصائص المادة النشطة بنجاح!")
print("📍 ستجد القائمة الجديدة في: View3D > KH-Tools > Basic Material > خصائص المادة النشطة")
print("📋 تأكد من تحديد كائن Mesh مع مادة نشطة لرؤية القائمة")

# اختبار سريع
obj = bpy.context.object
if obj and obj.active_material:
    print(f"\n🎯 اختبار: الكائن المحدد '{obj.name}' له مادة '{obj.active_material.name}'")
    print("✅ القائمة الفرعية ستظهر الآن!")
else:
    print("\n⚠️  لا يوجد كائن محدد أو مادة نشطة. حدد كائن Mesh مع مادة لرؤية القائمة.")
