import bpy
from bpy.types import Operator

class MATERIAL_OT_add_material(Operator):
    """إضافة مادة جديدة للكائن المحدد"""
    bl_idname = "material.add_material"
    bl_label = "إضافة مادة"
    bl_description = "إضافة مادة جديدة للكائن المحدد"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        obj = context.active_object
        if obj and obj.type == 'MESH':
            # إنشاء مادة جديدة
            mat = bpy.data.materials.new(name="مادة جديدة")
            mat.use_nodes = True
            
            # إضافة المادة للكائن
            obj.data.materials.append(mat)
            
            self.report({'INFO'}, "تم إضافة مادة جديدة")
        else:
            self.report({'WARNING'}, "يرجى تحديد كائن شبكي")
        
        return {'FINISHED'}

class MATERIAL_OT_remove_material(Operator):
    """إزالة المادة النشطة من الكائن المحدد"""
    bl_idname = "material.remove_material"
    bl_label = "إزالة مادة"
    bl_description = "إزالة المادة النشطة من الكائن المحدد"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        obj = context.active_object
        if obj and obj.type == 'MESH' and obj.active_material:
            # إزالة المادة النشطة
            mat_index = obj.active_material_index
            obj.data.materials.pop(index=mat_index)
            
            self.report({'INFO'}, "تم إزالة المادة")
        else:
            self.report({'WARNING'}, "لا توجد مادة نشطة لإزالتها")
        
        return {'FINISHED'}

class MATERIAL_OT_duplicate_material(Operator):
    """نسخ المادة النشطة"""
    bl_idname = "material.duplicate_material"
    bl_label = "نسخ مادة"
    bl_description = "إنشاء نسخة من المادة النشطة"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        obj = context.active_object
        if obj and obj.type == 'MESH' and obj.active_material:
            # نسخ المادة النشطة
            original_mat = obj.active_material
            new_mat = original_mat.copy()
            new_mat.name = original_mat.name + "_نسخة"
            
            # إضافة النسخة للكائن
            obj.data.materials.append(new_mat)
            
            self.report({'INFO'}, "تم نسخ المادة")
        else:
            self.report({'WARNING'}, "لا توجد مادة نشطة للنسخ")
        
        return {'FINISHED'}

# قائمة الفئات للتسجيل
classes = [
    MATERIAL_OT_add_material,
    MATERIAL_OT_remove_material,
    MATERIAL_OT_duplicate_material,
]

def register():
    """تسجيل جميع فئات العمليات"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """إلغاء تسجيل جميع فئات العمليات"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
